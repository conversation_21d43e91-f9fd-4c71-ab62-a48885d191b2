// Jest setup file for performance optimization

// Increase timeout for slower operations
jest.setTimeout(10000);

// Mock console methods to reduce noise during tests
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    // Keep error and warn for debugging
    error: originalConsole.error,
    warn: originalConsole.warn,
    // Suppress info, log, debug for performance
    info: jest.fn(),
    log: jest.fn(),
    debug: jest.fn(),
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// Global test performance optimizations
beforeEach(() => {
  // Clear all timers before each test
  jest.clearAllTimers();
  jest.useRealTimers();
});

afterEach(() => {
  // Clean up after each test
  jest.clearAllMocks();
  jest.restoreAllMocks();
});

// Performance monitoring (optional - can be removed in production)
const testStartTimes = new Map();

beforeEach(() => {
  const testName = expect.getState().currentTestName;
  if (testName) {
    testStartTimes.set(testName, Date.now());
  }
});

afterEach(() => {
  const testName = expect.getState().currentTestName;
  if (testName && testStartTimes.has(testName)) {
    const duration = Date.now() - testStartTimes.get(testName);
    if (duration > 5000) {
      console.warn(`⚠️  Slow test detected: "${testName}" took ${duration}ms`);
    }
    testStartTimes.delete(testName);
  }
});
