import { Logger } from '@nestjs/common';
import {
  WebNotificationLogResponseDto,
  WebNotificationHistoryResponseDto,
} from './enhanced-notification.dto';

export class WebNotificationOptimizer {
  private static readonly logger = new Logger(WebNotificationOptimizer.name);

  /**
   * Transform a standard notification log to a web-optimized version
   * @param notification The original notification log
   * @returns A web-optimized notification log
   */
  static optimizeNotification(
    notification: any,
  ): WebNotificationLogResponseDto {
    if (!notification) return notification;

    try {
      const webNotification: WebNotificationLogResponseDto = {
        id: notification.id,
        notification_type_id: notification.notification_type_id,
        title: notification.title || '',
        body: notification.body || '',
        data: notification.data || {},
        channels: notification.channels || [],
        status: notification.status || 'unknown',
        error: notification.error || undefined,
        created_at: notification.created_at,
        read_at: notification.read_at || null,
        is_read: !!notification.read_at,
        notification_type: notification.notification_type || undefined,
        module: notification.notification_type?.module || undefined,
        contextual_ids: this.extractContextualIds(notification.data),
      };

      return webNotification;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error optimizing notification ${notification?.id}: ${errorMessage}`,
      );

      // Return a safe fallback
      return {
        id: notification?.id || 'unknown',
        notification_type_id: notification?.notification_type_id || 'unknown',
        title: notification?.title || 'Notification',
        body: notification?.body || 'You have a new notification',
        data: {},
        channels: notification?.channels || [],
        status: notification?.status || 'unknown',
        error: undefined,
        created_at: notification?.created_at || new Date().toISOString(),
        read_at: notification?.read_at || null,
        is_read: !!notification?.read_at,
        notification_type: undefined,
        module: undefined,
        contextual_ids: {},
      };
    }
  }

  /**
   * Extract contextual IDs from notification data
   * @param data The notification data object
   * @returns Object containing extracted contextual IDs
   */
  private static extractContextualIds(data: any): Record<string, string> {
    if (!data || typeof data !== 'object') {
      return {};
    }

    const contextualIds: Record<string, string> = {};

    // Extract common ID patterns
    const idFields = [
      'event_id',
      'eventId',
      'post_id',
      'postId',
      'opportunity_id',
      'opportunityId',
      'quiz_id',
      'quizId',
      'raffle_id',
      'raffleId',
      'announcement_id',
      'announcementId',
      'user_id',
      'userId',
    ];

    for (const field of idFields) {
      if (data[field] && typeof data[field] === 'string') {
        // Normalize to snake_case for consistency
        const normalizedKey = field.replace(/([A-Z])/g, '_$1').toLowerCase();
        contextualIds[normalizedKey] = data[field];
      }
    }

    return contextualIds;
  }

  /**
   * Optimize a paginated list of notifications for web clients
   * @param paginatedData The original paginated data
   * @returns Optimized paginated data for web clients
   */
  static optimizePaginatedNotifications(
    paginatedData: any,
  ): WebNotificationHistoryResponseDto {
    if (!paginatedData || !paginatedData.data) {
      return {
        data: [],
        total: 0,
        page: 1,
        limit: 20,
        pages: 0,
        summary: {
          unread_count: 0,
          total_by_channel: {},
          total_by_module: {},
        },
      };
    }

    try {
      // Optimize each notification
      const optimizedNotifications = paginatedData.data.map(
        (notification: any) => this.optimizeNotification(notification),
      );

      // Generate summary statistics
      const summary = this.generateSummaryStatistics(optimizedNotifications);

      return {
        data: optimizedNotifications,
        total: paginatedData.total || 0,
        page: paginatedData.page || 1,
        limit: paginatedData.limit || 20,
        pages: paginatedData.pages || 0,
        summary,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error optimizing paginated notifications: ${errorMessage}`,
      );

      return {
        data: [],
        total: 0,
        page: 1,
        limit: 20,
        pages: 0,
        summary: {
          unread_count: 0,
          total_by_channel: {},
          total_by_module: {},
        },
      };
    }
  }

  /**
   * Generate summary statistics for web dashboard
   * @param notifications Array of optimized notifications
   * @returns Summary statistics object
   */
  private static generateSummaryStatistics(
    notifications: WebNotificationLogResponseDto[],
  ) {
    const summary = {
      unread_count: 0,
      total_by_channel: {} as Record<string, number>,
      total_by_module: {} as Record<string, number>,
    };

    for (const notification of notifications) {
      // Count unread notifications
      if (!notification.is_read) {
        summary.unread_count++;
      }

      // Count by channels
      for (const channel of notification.channels) {
        summary.total_by_channel[channel] =
          (summary.total_by_channel[channel] || 0) + 1;
      }

      // Count by module
      if (notification.module) {
        summary.total_by_module[notification.module] =
          (summary.total_by_module[notification.module] || 0) + 1;
      }
    }

    return summary;
  }

  /**
   * Optimize notification preferences for web clients
   * @param preferences The original notification preferences
   * @returns Optimized preferences for web clients
   */
  static optimizeNotificationPreferences(preferences: any[]): any {
    if (!preferences || !Array.isArray(preferences)) {
      return { preferences: [], modules: [] };
    }

    // Group preferences by module for better web UI organization
    const groupedPreferences = preferences.reduce(
      (acc, pref) => {
        const module =
          pref.module || pref.notification_type?.module || 'general';
        if (!acc[module]) {
          acc[module] = [];
        }
        acc[module].push({
          ...pref,
          // Add web-specific fields
          display_name: pref.notification_type?.name || pref.name || 'Unknown',
          description:
            pref.notification_type?.description || pref.description || '',
          default_channels: pref.notification_type?.default_channels || [],
        });
        return acc;
      },
      {} as Record<string, any[]>,
    );

    return {
      preferences: groupedPreferences,
      modules: Object.keys(groupedPreferences).sort(),
      total_preferences: preferences.length,
      enabled_count: preferences.filter((p) => p.enabled).length,
    };
  }

  /**
   * Optimize notification types for web clients
   * @param notificationTypes The original notification types
   * @returns Optimized notification types for web clients
   */
  static optimizeNotificationTypes(notificationTypes: any[]): any[] {
    if (!notificationTypes || !Array.isArray(notificationTypes)) {
      return [];
    }

    return notificationTypes.map((type) => ({
      ...type,
      // Add web-specific computed fields
      channel_count: type.default_channels?.length || 0,
      has_template: !!type.template,
      template_name: type.template?.name || null,
    }));
  }
}
