import { WebNotificationOptimizer } from './web-notification.dto';

describe('WebNotificationOptimizer', () => {
  const mockNotification = {
    id: 'notification-123',
    notification_type_id: 'type-456',
    title: 'Test Notification',
    body: 'This is a test notification',
    data: {
      event_id: 'event-123',
      post_id: 'post-456',
      opportunity_id: 'opportunity-789',
      title: 'Test Event',
    },
    channels: ['push', 'email'],
    status: 'sent',
    error: null,
    created_at: '2024-01-15T10:30:00Z',
    read_at: null,
    notification_type: {
      id: 'type-456',
      module: 'event',
      name: 'Event Notification',
    },
  };

  describe('optimizeNotification', () => {
    it('should optimize a notification for web clients', () => {
      const result =
        WebNotificationOptimizer.optimizeNotification(mockNotification);

      expect(result).toEqual({
        id: 'notification-123',
        notification_type_id: 'type-456',
        title: 'Test Notification',
        body: 'This is a test notification',
        data: mockNotification.data,
        channels: ['push', 'email'],
        status: 'sent',
        error: undefined,
        created_at: '2024-01-15T10:30:00Z',
        read_at: null,
        is_read: false,
        notification_type: mockNotification.notification_type,
        module: 'event',
        contextual_ids: {
          event_id: 'event-123',
          post_id: 'post-456',
          opportunity_id: 'opportunity-789',
        },
      });
    });

    it('should handle read notifications correctly', () => {
      const readNotification = {
        ...mockNotification,
        read_at: '2024-01-15T11:00:00Z',
      };

      const result =
        WebNotificationOptimizer.optimizeNotification(readNotification);

      expect(result.is_read).toBe(true);
      expect(result.read_at).toBe('2024-01-15T11:00:00Z');
    });

    it('should handle notifications without data gracefully', () => {
      const notificationWithoutData = {
        ...mockNotification,
        data: null,
      };

      const result = WebNotificationOptimizer.optimizeNotification(
        notificationWithoutData,
      );

      expect(result.data).toEqual({});
      expect(result.contextual_ids).toEqual({});
    });

    it('should handle null notification gracefully', () => {
      const result = WebNotificationOptimizer.optimizeNotification(null);
      expect(result).toBeNull();
    });

    it('should provide safe fallback for invalid notification', () => {
      const invalidNotification = { invalid: 'data' };

      const result =
        WebNotificationOptimizer.optimizeNotification(invalidNotification);

      expect(result.id).toBeUndefined(); // Since the invalid notification doesn't have an id
      expect(result.notification_type_id).toBeUndefined();
      expect(result.title).toBe('');
      expect(result.body).toBe('');
      expect(result.is_read).toBe(false);
      expect(result.contextual_ids).toEqual({});
    });
  });

  describe('optimizePaginatedNotifications', () => {
    it('should optimize paginated notification data', () => {
      const paginatedData = {
        data: [mockNotification],
        total: 1,
        page: 1,
        limit: 20,
        pages: 1,
      };

      const result =
        WebNotificationOptimizer.optimizePaginatedNotifications(paginatedData);

      expect(result.data).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.pages).toBe(1);
      expect(result.summary).toBeDefined();
      expect(result.summary.unread_count).toBe(1);
      expect(result.summary.total_by_channel).toEqual({
        push: 1,
        email: 1,
      });
      expect(result.summary.total_by_module).toEqual({
        event: 1,
      });
    });

    it('should handle empty paginated data', () => {
      const result =
        WebNotificationOptimizer.optimizePaginatedNotifications(null);

      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.summary.unread_count).toBe(0);
      expect(result.summary.total_by_channel).toEqual({});
      expect(result.summary.total_by_module).toEqual({});
    });

    it('should generate correct summary statistics', () => {
      const multipleNotifications = {
        data: [
          mockNotification,
          {
            ...mockNotification,
            id: 'notification-456',
            read_at: '2024-01-15T11:00:00Z',
            channels: ['in_app'],
            notification_type: { module: 'opportunity' },
          },
        ],
        total: 2,
        page: 1,
        limit: 20,
        pages: 1,
      };

      const result = WebNotificationOptimizer.optimizePaginatedNotifications(
        multipleNotifications,
      );

      expect(result.summary.unread_count).toBe(1);
      expect(result.summary.total_by_channel).toEqual({
        push: 1,
        email: 1,
        in_app: 1,
      });
      expect(result.summary.total_by_module).toEqual({
        event: 1,
        opportunity: 1,
      });
    });
  });

  describe('optimizeNotificationPreferences', () => {
    it('should optimize notification preferences for web clients', () => {
      const preferences = [
        {
          id: 'pref-1',
          notification_type_id: 'type-1',
          enabled: true,
          module: 'event',
          notification_type: {
            name: 'Event Created',
            description: 'When a new event is created',
            default_channels: ['push', 'email'],
          },
        },
        {
          id: 'pref-2',
          notification_type_id: 'type-2',
          enabled: false,
          module: 'opportunity',
          notification_type: {
            name: 'Opportunity Available',
            description: 'When a new opportunity is available',
            default_channels: ['push'],
          },
        },
      ];

      const result =
        WebNotificationOptimizer.optimizeNotificationPreferences(preferences);

      expect(result.preferences).toBeDefined();
      expect(result.preferences.event).toHaveLength(1);
      expect(result.preferences.opportunity).toHaveLength(1);
      expect(result.modules).toEqual(['event', 'opportunity']);
      expect(result.total_preferences).toBe(2);
      expect(result.enabled_count).toBe(1);

      // Check enhanced fields
      expect(result.preferences.event[0].display_name).toBe('Event Created');
      expect(result.preferences.event[0].description).toBe(
        'When a new event is created',
      );
      expect(result.preferences.event[0].default_channels).toEqual([
        'push',
        'email',
      ]);
    });

    it('should handle empty preferences array', () => {
      const result = WebNotificationOptimizer.optimizeNotificationPreferences(
        [],
      );

      expect(result.preferences).toEqual({});
      expect(result.modules).toEqual([]);
      expect(result.total_preferences).toBe(0);
      expect(result.enabled_count).toBe(0);
    });

    it('should handle null preferences', () => {
      const result =
        WebNotificationOptimizer.optimizeNotificationPreferences(null);

      expect(result.preferences).toEqual([]);
      expect(result.modules).toEqual([]);
    });
  });

  describe('optimizeNotificationTypes', () => {
    it('should optimize notification types for web clients', () => {
      const notificationTypes = [
        {
          id: 'type-1',
          name: 'Event Created',
          default_channels: ['push', 'email'],
          template: { id: 'template-1', name: 'Event Template' },
        },
        {
          id: 'type-2',
          name: 'Opportunity Available',
          default_channels: ['push'],
          template: null,
        },
      ];

      const result =
        WebNotificationOptimizer.optimizeNotificationTypes(notificationTypes);

      expect(result).toHaveLength(2);
      expect(result[0].channel_count).toBe(2);
      expect(result[0].has_template).toBe(true);
      expect(result[0].template_name).toBe('Event Template');
      expect(result[1].channel_count).toBe(1);
      expect(result[1].has_template).toBe(false);
      expect(result[1].template_name).toBeNull();
    });

    it('should handle empty notification types array', () => {
      const result = WebNotificationOptimizer.optimizeNotificationTypes([]);
      expect(result).toEqual([]);
    });

    it('should handle null notification types', () => {
      const result = WebNotificationOptimizer.optimizeNotificationTypes(null);
      expect(result).toEqual([]);
    });
  });
});
