/**
 * Cache time-to-live constants in seconds
 */
export const CACHE_TTL = {
  // Short-lived caches
  THIRTY_SECONDS: 30,
  ONE_MINUTE: 60,
  FIVE_MINUTES: 300,
  TEN_MINUTES: 600,
  THIRTY_MINUTES: 1800,
  ONE_HOUR: 3600,

  // Medium-lived caches
  THREE_HOURS: 10800,
  SIX_HOURS: 21600,
  TWELVE_HOURS: 43200,
  ONE_DAY: 86400,

  // Long-lived caches
  THREE_DAYS: 259200,
  SEVEN_DAYS: 604800,
  TWO_WEEKS: 1209600,
  ONE_MONTH: 2592000,

  // Special purpose TTLs
  RANK: 300, // 5 minutes for leaderboard ranks
  ACTIVE_QUIZ: 60, // 1 minute for active quizzes
  USER_SESSION: 1800, // 30 minutes for user session data
  REFERENCE_DATA: 86400, // 24 hours for reference data
};

/**
 * Cache prefix constants for different entity types
 * Used for namespacing cache keys and for bulk invalidation
 */
export const CACHE_PREFIXES = {
  // Core entities
  COUNTRY: 'country',
  INSTITUTION: 'institution',
  CLUB: 'club',
  POST: 'post',
  ORGANISATION: 'organisation',
  STUDENT_PROFILE: 'student_profile',
  USER: 'user',
  CAREER: 'career',
  SKILL: 'skill',
  SKILL_CATEGORY: 'skill_category',
  AUTH: 'auth',

  // Educational content
  QUIZ: 'quiz',
  QUESTION: 'question',
  QUESTION_BANK: 'question_bank',
  LEADERBOARD: 'leaderboard',

  // Engagement features
  EVENT: 'event',
  OPPORTUNITY: 'opportunity',
  TRENDING: 'trending',
  NOTIFICATION: 'notification',
  POINT_SYSTEM: 'point_system',
  RAFFLE: 'raffle',

  // System features
  STATS: 'stats',
  CONFIG: 'config',
  METRICS: 'metrics',

  // Examples
  EXAMPLE: 'example',
} as const;

/**
 * Cache key patterns for common operations
 */
export const CACHE_PATTERNS = {
  ALL_ENTITIES: (prefix: string) => `*:${prefix}:*`,
  ENTITY_BY_ID: (prefix: string, id: string) => `${prefix}:${id}`,
  LIST_BY_ROLE: (prefix: string, role: string) => `${prefix}:list:${role}`,
  USER_DATA: (userId: string) => `user:${userId}:*`,
};

/**
 * Cache strategy types for different caching approaches
 */
export enum CacheStrategy {
  CACHE_ASIDE = 'cache_aside', // Check cache first, then source
  WRITE_THROUGH = 'write_through', // Update cache and source together
  WRITE_BEHIND = 'write_behind', // Update cache first, then source asynchronously
  REFRESH_AHEAD = 'refresh_ahead', // Proactively refresh cache before expiration
}

export const CacheRoutes = {
  HEALTH: 'health',
  STATS: 'stats',
  DELETE_ALL: 'all',
  DELETE_BY_PREFIX: 'prefix/:prefix',
  WARMUP_CACHE: 'warmup',
  RUN_CLEANUP: 'cleanup',
};
