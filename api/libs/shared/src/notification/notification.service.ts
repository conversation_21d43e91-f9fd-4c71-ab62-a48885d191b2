import { Injectable, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import {
  MultipleDeviceNotificationDto,
  SendNotificationDto,
  TopicNotificationDto,
} from './dto/notification.dto';
import { FirebaseAdminService } from '@/firebase/firebase-admin.provider';
import { Subject } from 'rxjs';
import { DrizzleService } from '../drizzle/drizzle.service';
import { device_tokens } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { UserRepository } from '@/repositories/user.repository';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly firebaseAdmin: admin.app.App;

  private readonly notificationSubjects: Map<string, Subject<any>> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map();

  constructor(
    private readonly firebaseAdminService: FirebaseAdminService,
    private readonly drizzle: DrizzleService,
    private readonly userRepository: UserRepository,
  ) {
    this.firebaseAdmin = this.firebaseAdminService.getAdminInstance();
    this.notificationSubjects.set('wish-list-joined', new Subject<any>());
  }
  async sendNotification({
    token,
    notification: { title, body },
    data,
  }: SendNotificationDto) {
    if (!token || typeof token !== 'string' || token.length < 20) {
      this.logger.error('Invalid FCM registration token:', token);
      throw new Error('Invalid FCM registration token');
    }
    try {
      // Convert all data values to strings for FCM compatibility
      const stringifiedData: Record<string, string> = {};

      // Add timestamp
      stringifiedData.timestamp = `${new Date().getTime()}`;

      // Convert all other data values to strings
      if (data) {
        Object.entries(data).forEach(([key, value]) => {
          if (value === undefined || value === null) {
            stringifiedData[key] = '';
          } else if (typeof value === 'object') {
            stringifiedData[key] = JSON.stringify(value);
          } else {
            stringifiedData[key] = String(value);
          }
        });
      }

      const message = {
        token: token,
        notification: {
          title: title,
          body: body,
        },
        data: stringifiedData,
      };

      const response = await this.firebaseAdmin.messaging().send(message);
      return { status: true, messageId: response };
    } catch (error) {
      this.logger.error('Error sending notification', error);
      throw error;
    }
  }

  async sendNotificationToMultipleTokens({
    tokens,
    title,
    body,
    icon,
    data,
  }: MultipleDeviceNotificationDto) {
    // Convert all data values to strings for FCM compatibility
    const stringifiedData: Record<string, string> = {};

    // Add timestamp
    stringifiedData.timestamp = `${new Date().getTime()}`;

    // Convert all other data values to strings
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        if (value === undefined || value === null) {
          stringifiedData[key] = '';
        } else if (typeof value === 'object') {
          stringifiedData[key] = JSON.stringify(value);
        } else {
          stringifiedData[key] = String(value);
        }
      });
    }

    const message = {
      notification: {
        title,
        body,
        icon,
      },
      data: stringifiedData,
      tokens,
    };

    try {
      const response = await this.firebaseAdmin
        .messaging()
        .sendEachForMulticast(message);
      return {
        success: true,
        message: `Successfully sent ${response.successCount} messages; ${response.failureCount} failed.`,
      };
    } catch (error) {
      this.logger.error('Error sending messages', error);
      return { success: false, message: 'Failed to send notifications' };
    }
  }

  async sendTopicNotification({
    topic,
    title,
    body,
    icon,
    data,
  }: TopicNotificationDto) {
    // Convert all data values to strings for FCM compatibility
    const stringifiedData: Record<string, string> = {};

    // Add timestamp
    stringifiedData.timestamp = `${new Date().getTime()}`;

    // Convert all other data values to strings
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        if (value === undefined || value === null) {
          stringifiedData[key] = '';
        } else if (typeof value === 'object') {
          stringifiedData[key] = JSON.stringify(value);
        } else {
          stringifiedData[key] = String(value);
        }
      });
    }

    const message = {
      notification: {
        title,
        body,
        icon,
      },
      data: stringifiedData,
      topic,
    };

    try {
      await this.firebaseAdmin.messaging().send(message);
      return { success: true, message: 'Topic notification sent successfully' };
    } catch (error) {
      this.logger.error('Error sending message', error);
      return { success: false, message: 'Failed to send topic notification' };
    }
  }

  async subscribeToTopic(token: string, topic: string): Promise<void> {
    try {
      await this.firebaseAdmin.messaging().subscribeToTopic(token, topic);
    } catch (error) {
      throw error;
    }
  }
  async unsubscribeFromTopic(token: string, topic: string): Promise<void> {
    try {
      await this.firebaseAdmin.messaging().unsubscribeFromTopic(token, topic);
    } catch (error) {
      throw error;
    }
  }
  async getAccessToken(): Promise<any> {
    try {
      const response = await this.firebaseAdmin
        .messaging()
        .app.options.credential!.getAccessToken();
      return { message: response };
    } catch (error) {
      this.logger.error('Error getting access token', error);
      throw error;
    }
  }
  subscribe(eventType: string, eventId: string) {
    this.subscriptions.get(eventType)?.add(eventId);
    return { status: true, message: 'Subscribed to notification stream' };
  }
  unsubscribe(event: string, eventId: string) {
    this.subscriptions.get(event)?.delete(eventId);
    return { status: true, message: 'Unsubscribed from notification stream' };
  }

  sendNotificationToStream(event: string, data: any) {
    return this.notificationSubjects.get(event)?.next(data);
  }
  getNotificationStream(eventType: string) {
    return this.notificationSubjects.get(eventType)?.asObservable();
  }
  emitWishListJoined(payload: any) {
    this.notificationSubjects.get('wish-list-joined')?.next(payload);
  }
  /**
   * Send notification to a user by userId (fetches FCM token from device_token table)
   * @param userId The user's ID
   * @param notification Notification object { title, body }
   * @param data Additional data payload
   */
  /**
   * Utility function to validate that a user exists, is active, and not deleted
   * Uses the centralized UserRepository validation method
   * @param userId The user ID to validate
   * @param context Context for logging (e.g., 'notification')
   * @returns The user object if valid, null if not valid
   */
  private async validateActiveUser(
    userId: string,
    context: string = 'notification',
  ): Promise<{ state: string } | null> {
    try {
      // Use centralized validation from UserRepository
      const validUser = await this.userRepository.validateActiveUser(userId);
      if (!validUser) {
        this.logger.warn(
          `User validation failed for ${context} (user ${userId})`,
        );
        return null;
      }

      return validUser;
    } catch (error: any) {
      this.logger.error(
        `Error validating user ${userId} for ${context}:`,
        error?.stack,
      );
      return null;
    }
  }

  /**
   * Send notification to a user by userId (fetches FCM token from device_token table)
   * @param userId The user's ID
   * @param notification Notification object { title, body }
   * @param data Additional data payload
   */
  async sendNotificationToUser(
    userId: string,
    notification: { title: string; body: string },
    data: any,
  ) {
    // Check if user is active
    const user = await this.validateActiveUser(userId, 'push notification');
    if (!user) {
      return { status: false, message: 'User not found or not active.' };
    }

    // Fetch token from device_token table
    const [deviceToken] = await this.drizzle.db
      .select()
      .from(device_tokens)
      .where(eq(device_tokens.user_id, userId));
    if (!deviceToken || !deviceToken.token || deviceToken.token.length < 20) {
      this.logger.warn(
        `User ${userId} does not have a valid FCM token. Skipping notification.`,
      );
      return { status: false, message: 'No valid FCM token for user.' };
    }

    return this.sendNotification({
      token: deviceToken.token,
      notification,
      data,
    });
  }
}
