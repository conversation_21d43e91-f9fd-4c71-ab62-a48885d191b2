import { DynamicModule, Module } from '@nestjs/common';
import { SharedService } from './shared.service';
import { dotenvLoader, TypedConfigModule } from 'nest-typed-config';
import { EnvConfig, envConfigSchema } from './dto/env-config.dto';
import { ZodValidationPipe } from 'nestjs-zod';
import { APP_PIPE } from '@nestjs/core';
import { LoggerModule } from 'nestjs-pino';
import * as dotenv from 'dotenv';

// Ensure .env is loaded before validation
dotenv.config();

@Module({
  imports: [
    TypedConfigModule.forRoot({
      isGlobal: true,
      load: dotenvLoader(),
      schema: EnvConfig,
      validate: (config) => envConfigSchema.parse(config),
    }),
  ],
  providers: [SharedService],
  exports: [SharedService],
})
export class SharedModule {
  static registerZodValidationPipe(): DynamicModule {
    const providers = [
      {
        provide: APP_PIPE,
        useClass: ZodValidationPipe,
      },
    ];

    return {
      module: SharedModule,
      providers,
    };
  }

  static registerPinoLogger(): DynamicModule {
    return {
      module: SharedModule,
      imports: [
        LoggerModule.forRootAsync({
          inject: [EnvConfig],
          useFactory: (envConfig: EnvConfig) => {
            const isDevelopment =
              envConfig.NODE_ENV === 'development' ||
              envConfig.NODE_ENV === 'local';

            return {
              pinoHttp: {
                level: isDevelopment ? 'debug' : 'info',
                customProps: () => ({
                  context: 'HTTP',
                }),
                transport: isDevelopment
                  ? {
                      target: 'pino-pretty',
                      options: {
                        colorize: true,
                        translateTime: 'dd-mm-yyyy HH:MM:ss',
                        singleLine: true,
                        messageKey: 'msg',
                        ignore: 'pid,hostname,email-*,Job email-*',
                      },
                    }
                  : undefined,
                // Skip logging for health check endpoints to reduce noise
                customLogLevel: (req, res) => {
                  if (req.url?.includes('/health')) {
                    return 'silent';
                  }
                  if (res.statusCode >= 500) {
                    return 'error';
                  }
                  if (res.statusCode >= 400) {
                    return 'warn';
                  }
                  return 'info';
                },
                // Skip logging for successful health checks
                autoLogging: {
                  ignore: (req) => req.url?.includes('/health') === true,
                },
                // Redact sensitive information
                redact: {
                  paths: [
                    'req.headers.authorization',
                    'req.headers.cookie',
                    'req.headers["x-api-key"]',
                    'req.body.password',
                    'req.body.token',
                  ],
                  censor: '***REDACTED***',
                },
              },
            };
          },
        }),
      ],
    };
  }
}
