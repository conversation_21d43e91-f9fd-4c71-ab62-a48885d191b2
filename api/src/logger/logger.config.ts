import { Params } from 'nestjs-pino';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { randomUUID } from 'crypto';

/**
 * Creates logger configuration based on environment
 * @param envConfig Environment configuration
 * @returns Logger module options
 */
export function createLoggerConfig(envConfig: EnvConfig): Params {
  const isDevelopment =
    envConfig.NODE_ENV === 'development' || envConfig.NODE_ENV === 'local';
  const isTest = envConfig.NODE_ENV === 'testing';

  // Base configuration
  const config: Params = {
    pinoHttp: {
      level: isDevelopment ? 'debug' : 'info',
      transport: isDevelopment
        ? { target: 'pino-pretty', options: { singleLine: true } }
        : undefined,
      customProps: () => ({
        context: 'HTTP',
      }),
      // Add request ID to all logs
      genReqId: (req) => {
        const reqWithId = req as any;
        return reqWithId.id ?? randomUUID();
      },
      // Customize log output
      formatters: {
        level: (label) => {
          return { level: label };
        },
      },
      // Custom serializer to filter out email job logs
      serializers: {
        req: (req) => {
          return req;
        },
        res: (res) => {
          return res;
        },
        err: (err) => {
          // Filter out email job logs in non-development environments
          if (
            process.env.NODE_ENV !== 'development' &&
            err &&
            err.message &&
            err.message.includes('email-')
          ) {
            return { ...err, level: 'silent' };
          }
          return err;
        },
      },
      // Redact sensitive information
      redact: {
        paths: [
          'req.headers.authorization',
          'req.headers.cookie',
          'req.headers["x-api-key"]',
          'req.body.password',
          'req.body.token',
          'res.headers["set-cookie"]',
        ],
        censor: '***REDACTED***',
      },
    },
  };

  // Add log filtering for production
  if (!isDevelopment) {
    config.pinoHttp = {
      ...config.pinoHttp,
      // Skip logging for health check endpoints to reduce noise
      customLogLevel: (req, res) => {
        if (req.url?.includes('/health')) {
          return 'silent';
        }
        if ((res as any).statusCode >= 500) {
          return 'error';
        }
        if ((res as any).statusCode >= 400) {
          return 'warn';
        }
        return 'info';
      },
      // Skip logging for successful health checks
      autoLogging: {
        ignore: (req) => Boolean(req.url?.includes('/health')),
      },
    };
  }

  // Disable logging completely in test environment
  if (isTest) {
    config.pinoHttp = {
      ...config.pinoHttp,
      level: 'silent',
    };
  }

  return config;
}
