import { Controller, Get, Logger, Inject, Optional } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { RedisReadinessService } from '@app/shared/redis/redis-readiness.service';
import { Public } from '@/guards/guard.decorator';
import { HealthRoutes } from '@app/shared/constants/health.constants';

@ApiTags('Health')
@Controller({ version: '1', path: 'health' })
export class HealthController {
  private readonly logger = new Logger(HealthController.name);
  private readonly redisAvailable: boolean;

  constructor(
    private readonly drizzleService: DrizzleService,
    @Optional()
    @Inject(RedisService)
    private readonly redisService?: RedisService,
    @Optional()
    @Inject(RedisReadinessService)
    private readonly redisReadinessService?: RedisReadinessService,
  ) {
    this.redisAvailable = !!this.redisService;
    if (!this.redisAvailable) {
      this.logger.warn(
        'Redis service is not available. Redis health checks will be skipped.',
      );
    }
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  async getHealth() {
    // Check database connection
    const dbStatus = await this.checkDatabase();

    // Check Redis connection
    const redisStatus = await this.checkRedis();

    // Overall status is healthy only if all available checks pass
    // If Redis is not available, only consider database health
    const isHealthy =
      dbStatus.healthy && (redisStatus.available ? redisStatus.healthy : true);

    const response = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbStatus,
        redis: redisStatus,
      },
    };

    // Result logging removed to reduce resource consumption

    return response;
  }

  @Get(HealthRoutes.DATABASE)
  @Public()
  @ApiOperation({ summary: 'Get database health status' })
  @ApiResponse({ status: 200, description: 'Database health check' })
  async getDatabaseHealth() {
    const status = await this.checkDatabase();
    const poolStatus = this.drizzleService.getPoolStatus();

    return {
      status: status.healthy ? 'healthy' : 'unhealthy',
      details: {
        ...status,
        pool: poolStatus,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get(HealthRoutes.DATABASE_METRICS)
  @Public()
  @ApiOperation({ summary: 'Get detailed database pool metrics' })
  @ApiResponse({ status: 200, description: 'Database pool metrics' })
  async getDatabaseMetrics() {
    const poolMetrics = this.drizzleService.getPoolMetrics();
    const poolStatus = this.drizzleService.getPoolStatus();

    return {
      metrics: poolMetrics,
      status: poolStatus,
      timestamp: new Date().toISOString(),
    };
  }

  @Get(HealthRoutes.REDIS)
  @Public()
  @ApiOperation({ summary: 'Get Redis health status' })
  @ApiResponse({ status: 200, description: 'Redis health check' })
  async getRedisHealth() {
    const status = await this.checkRedis();

    return {
      status: status.healthy ? 'healthy' : 'unhealthy',
      details: status,
      timestamp: new Date().toISOString(),
    };
  }

  @Get(HealthRoutes.REDIS_READINESS)
  @Public()
  @ApiOperation({ summary: 'Get Redis readiness status' })
  @ApiResponse({ status: 200, description: 'Redis readiness check' })
  async getRedisReadiness() {
    if (!this.redisReadinessService) {
      return {
        status: 'unavailable',
        details: { error: 'Redis readiness service not available' },
        timestamp: new Date().toISOString(),
      };
    }

    const connectionStatus = this.redisReadinessService.getConnectionStatus();
    const isReady = this.redisReadinessService.isRedisReady();

    return {
      status: isReady ? 'ready' : 'not-ready',
      details: connectionStatus,
      timestamp: new Date().toISOString(),
    };
  }

  private async checkDatabase(): Promise<{
    healthy: boolean;
    latency?: number;
    error?: string;
  }> {
    try {
      const startTime = Date.now();
      const isHealthy = await this.drizzleService.isHealthy();
      const latency = Date.now() - startTime;

      if (!isHealthy) {
        return { healthy: false, error: 'Database health check failed' };
      }

      return { healthy: true, latency };
    } catch (error: any) {
      this.logger.error('Database health check error', error);
      return {
        healthy: false,
        error: error.message || 'Unknown database error',
      };
    }
  }

  private async checkRedis(): Promise<{
    healthy: boolean;
    latency?: number;
    error?: string;
    available: boolean;
  }> {
    // If Redis service is not available, return appropriate status
    if (!this.redisAvailable || !this.redisService) {
      return {
        healthy: false,
        available: false,
        error: 'Redis service is not available',
      };
    }

    try {
      const startTime = Date.now();
      const isHealthy = await this.redisService.isHealthy();
      const latency = Date.now() - startTime;

      if (!isHealthy) {
        return {
          healthy: false,
          available: true,
          error: 'Redis health check failed',
        };
      }

      return {
        healthy: true,
        available: true,
        latency,
      };
    } catch (error: any) {
      this.logger.error('Redis health check error', error);
      return {
        healthy: false,
        available: true,
        error: error.message || 'Unknown Redis error',
      };
    }
  }
}
