import * as admin from 'firebase-admin';
import { Injectable, Logger } from '@nestjs/common';
import { EnvConfig } from '@app/shared/dto/env-config.dto';

@Injectable()
export class FirebaseAdminService {
  private firebaseAdminInstance: admin.app.App | null = null;
  private readonly logger = new Logger(FirebaseAdminService.name);

  constructor(private readonly envConfig: EnvConfig) {
    this.initializeFirebaseAdmin();
  }

  private async initializeFirebaseAdmin() {
    if (!this.firebaseAdminInstance) {
      try {
        const serviceAccountBase64 =
          this.envConfig.FIREBASE_SERVICE_ACCOUNT_BASE64;

        if (!serviceAccountBase64) {
          throw new Error('Firebase service account credentials not available');
        }

        const serviceAccountJson = Buffer.from(
          serviceAccountBase64,
          'base64',
        ).toString('utf8');

        this.logger.log('Successfully loaded Firebase credentials');

        const serviceAccount = JSON.parse(serviceAccountJson);
        this.logger.log(`Firebase project_id: ${serviceAccount.project_id}`);

        this.firebaseAdminInstance = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });

        this.logger.log('Firebase Admin SDK initialized successfully');
      } catch (error) {
        this.logger.error('Failed to initialize Firebase Admin SDK:', error);
        throw new Error('Failed to initialize Firebase Admin SDK');
      }
    }
  }

  getAdminInstance(): admin.app.App {
    if (!this.firebaseAdminInstance) {
      this.initializeFirebaseAdmin().catch((error) => {
        this.logger.error(
          'Failed to initialize Firebase Admin SDK on demand:',
          error,
        );
      });
    }
    return this.firebaseAdminInstance as admin.app.App;
  }
}
