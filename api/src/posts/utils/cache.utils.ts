import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';
import { postStatus, postType } from '@/db/schema/posts';

const logger = new Logger('PostCache');

/**
 * Generates a cache key for a specific post
 */
export const generatePostKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateResourceKey(id, prefix);
};

/**
 * Invalidates all post-related caches including role-specific and type-specific caches
 */
export const invalidatePostCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Type-specific caches
    ...postType.map((type) => `type:${type}`),
    // Status-specific caches
    ...postStatus.map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Specific post cache
    id,
    // User-specific caches
    ...Object.values(user_roles).map((role) => `all:${role}:*`),
  ];

  try {
    await cacheService.invalidateMany(keysToInvalidate, prefix);
    logger.debug('Post caches invalidated successfully');
  } catch (error) {
    logger.warn('Failed to invalidate post caches', error);
  }
};
