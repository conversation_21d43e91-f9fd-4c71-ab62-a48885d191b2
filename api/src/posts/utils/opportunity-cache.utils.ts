import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';
import { post_types, post_statuses } from '@/db/schema/posts';

/**
 * Generates a cache key for a specific opportunity
 */
export const generateOpportunityKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateKey(id, prefix);
};

/**
 * Invalidates all opportunity-related caches including role-specific and status-specific caches
 */
export const invalidateOpportunityCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Type-specific caches (opportunities are posts with type OPPORTUNITY)
    `type:${post_types.OPPORTUNITY}`,
    // Status-specific caches
    ...Object.values(post_statuses).map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Club-specific caches (pattern match)
    'club-posts:*',
    // Student-specific caches (pattern match)
    'student-posts:*',
    // Specific opportunity cache
    id,
  ];

  try {
    await Promise.all(
      keysToInvalidate.map(async (key) => {
        if (key.includes('*')) {
          // Use pattern invalidation for wildcard keys
          const pattern = `*:${prefix}:*:${key}`;
          await cacheService.invalidatePattern(pattern);
        } else {
          // Direct key invalidation
          const fullKey = cacheService.generateKey(key, prefix);
          await cacheService.del(fullKey);
        }
      }),
    );
  } catch (error) {
    // Log error but don't throw to avoid breaking the main operation
    console.error('Failed to invalidate opportunity caches:', error);
  }
};

/**
 * Invalidates post-related caches when opportunities are created/updated/deleted
 * Since opportunities are posts with type OPPORTUNITY, we need to invalidate post caches too
 */
export const invalidatePostCachesForOpportunity = async (
  cacheService: CacheService,
  id: string,
  postPrefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Type-specific caches
    `type:${post_types.OPPORTUNITY}`,
    // Status-specific caches
    ...Object.values(post_statuses).map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Club-specific caches (pattern match)
    'club-posts:*',
    // Student-specific caches (pattern match)
    'student-posts:*',
    // Specific post cache
    id,
  ];

  try {
    await Promise.all(
      keysToInvalidate.map(async (key) => {
        if (key.includes('*')) {
          // Use pattern invalidation for wildcard keys
          const pattern = `*:${postPrefix}:*:${key}`;
          await cacheService.invalidatePattern(pattern);
        } else {
          // Direct key invalidation
          const fullKey = cacheService.generateKey(key, postPrefix);
          await cacheService.del(fullKey);
        }
      }),
    );
  } catch (error) {
    // Log error but don't throw to avoid breaking the main operation
    console.error('Failed to invalidate post caches for opportunity:', error);
  }
};
