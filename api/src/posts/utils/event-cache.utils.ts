import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';
import { post_types, post_statuses } from '@/db/schema/posts';

/**
 * Generates a cache key for a specific event
 */
export const generateEventKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateKey(id, prefix);
};

/**
 * Invalidates all event-related caches including role-specific and status-specific caches
 */
export const invalidateEventCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Type-specific caches (events are posts with type EVENT)
    `type:${post_types.EVENT}`,
    // Status-specific caches
    ...Object.values(post_statuses).map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Club-specific caches (pattern match)
    'club-posts:*',
    // Student-specific caches (pattern match)
    'student-posts:*',
    // Specific event cache
    id,
  ];

  try {
    await Promise.all(
      keysToInvalidate.map(async (key) => {
        if (key.includes('*')) {
          // Use pattern invalidation for wildcard keys
          const pattern = `*:${prefix}:*:${key}`;
          await cacheService.invalidatePattern(pattern);
        } else {
          // Direct key invalidation
          const fullKey = cacheService.generateKey(key, prefix);
          await cacheService.del(fullKey);
        }
      }),
    );
  } catch (error) {
    // Log error but don't throw to avoid breaking the main operation
    console.error('Failed to invalidate event caches:', error);
  }
};

/**
 * Invalidates post-related caches when events are created/updated/deleted
 * Since events are posts with type EVENT, we need to invalidate post caches too
 */
export const invalidatePostCachesForEvent = async (
  cacheService: CacheService,
  id: string,
  postPrefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Type-specific caches
    `type:${post_types.EVENT}`,
    // Status-specific caches
    ...Object.values(post_statuses).map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Club-specific caches (pattern match)
    'club-posts:*',
    // Student-specific caches (pattern match)
    'student-posts:*',
    // Specific post cache
    id,
  ];

  try {
    await Promise.all(
      keysToInvalidate.map(async (key) => {
        if (key.includes('*')) {
          // Use pattern invalidation for wildcard keys
          const pattern = `*:${postPrefix}:*:${key}`;
          await cacheService.invalidatePattern(pattern);
        } else {
          // Direct key invalidation
          const fullKey = cacheService.generateKey(key, postPrefix);
          await cacheService.del(fullKey);
        }
      }),
    );
  } catch (error) {
    // Log error but don't throw to avoid breaking the main operation
    console.error('Failed to invalidate post caches for event:', error);
  }
};
