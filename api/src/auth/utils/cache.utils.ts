import { CacheService } from '@app/shared/redis/cache.service';
import { CACHE_PREFIXES } from '@app/shared/constants/cache.constant';

/**
 * Cache prefix for auth-related caches
 */
export const AUTH_CACHE_PREFIX = CACHE_PREFIXES.AUTH;

/**
 * Generate a cache key for the waiting list
 * @param params Parameters that affect the waiting list query
 * @returns Cache key for the waiting list
 */
export const generateWaitingListKey = (params: {
  page?: number;
  limit?: number;
  sort?: string;
  order?: string;
  search?: string;
}): string => {
  const {
    page = 1,
    limit = 10,
    sort = 'created_at',
    order = 'desc',
    search = '',
  } = params;
  return `waiting-list:${page}:${limit}:${sort}:${order}:${search ? 'search' : 'all'}`;
};

/**
 * Invalidate all waiting list caches
 * @param cacheService The cache service instance
 */
export const invalidateWaitingListCaches = async (
  cacheService: CacheService,
): Promise<void> => {
  try {
    // Use pattern matching to invalidate all waiting list caches
    await cacheService.invalidatePattern(`${AUTH_CACHE_PREFIX}:waiting-list:*`);
  } catch (error) {
    console.error('Failed to invalidate waiting list caches:', error);
  }
};
