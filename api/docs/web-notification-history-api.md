# Web Notification History API

## Overview

The Web Notification History API provides enhanced notification history functionality specifically optimized for web clients. It extends the existing notification history endpoint with web-specific features including detailed statistics, contextual IDs, and enhanced data formatting.

## Endpoint

```
GET /api/v1/notifications/history
```

## Authentication

Requires <PERSON><PERSON> token authentication with appropriate permissions.

## Headers

```
Authorization: Bearer <token>
x-client-type: web
```

**Important**: The `x-client-type: web` header is required to receive web-optimized responses.

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Items per page (default: 50, max: 100 for web) |
| `read` | boolean | No | Filter by read status (true/false) |
| `from_date` | string | No | Start date (ISO 8601 format) |
| `to_date` | string | No | End date (ISO 8601 format) |
| `notification_type_id` | string | No | Filter by notification type UUID |
| `channels` | string[] | No | Filter by channels (push, email, in_app) |
| `status` | string | No | Filter by delivery status (sent, failed) |
| `module` | string | No | Filter by module (event, opportunity, post, quiz, etc.) |
| `search` | string | No | Text search in title/body |
| `sort` | string | No | Sort field (default: created_at) |
| `order` | string | No | Sort order (asc, desc) |

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid-here",
        "notification_type_id": "uuid-here",
        "title": "New Event Available",
        "body": "Check out this exciting new event!",
        "data": {
          "event_id": "uuid-here",
          "post_id": "uuid-here",
          "title": "Event Title"
        },
        "channels": ["push", "email"],
        "status": "sent",
        "error": null,
        "created_at": "2024-01-15T10:30:00Z",
        "read_at": null,
        "is_read": false,
        "notification_type": {
          "id": "uuid-here",
          "name": "Event Created",
          "module": "event"
        },
        "module": "event",
        "contextual_ids": {
          "event_id": "uuid-here",
          "post_id": "uuid-here"
        }
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 50,
    "pages": 1,
    "summary": {
      "unread_count": 5,
      "total_by_channel": {
        "push": 15,
        "email": 10,
        "in_app": 8
      },
      "total_by_module": {
        "event": 10,
        "opportunity": 8,
        "post": 7
      }
    }
  }
}
```

## Web-Specific Features

### 1. Enhanced Data Structure

Each notification includes:
- `is_read`: Boolean indicating read status
- `module`: Module type extracted from notification type
- `contextual_ids`: Extracted entity IDs for easy navigation

### 2. Summary Statistics

The response includes a `summary` object with:
- `unread_count`: Total unread notifications
- `total_by_channel`: Count of notifications by delivery channel
- `total_by_module`: Count of notifications by module type

### 3. Contextual IDs

The `contextual_ids` field extracts relevant entity IDs from the notification data:
- `event_id`: For event-related notifications
- `post_id`: For post-related notifications
- `opportunity_id`: For opportunity-related notifications
- `quiz_id`: For quiz-related notifications
- `raffle_id`: For raffle-related notifications

### 4. Higher Pagination Limits

Web clients support higher pagination limits (up to 100 items per page) compared to mobile clients.

## Usage Examples

### Get Recent Notifications

```bash
curl -X GET "https://api.example.com/api/v1/notifications/history?page=1&limit=20" \
  -H "Authorization: Bearer <token>" \
  -H "x-client-type: web"
```

### Filter by Module

```bash
curl -X GET "https://api.example.com/api/v1/notifications/history?module=event&read=false" \
  -H "Authorization: Bearer <token>" \
  -H "x-client-type: web"
```

### Filter by Date Range

```bash
curl -X GET "https://api.example.com/api/v1/notifications/history?from_date=2024-01-01T00:00:00Z&to_date=2024-01-31T23:59:59Z" \
  -H "Authorization: Bearer <token>" \
  -H "x-client-type: web"
```

### Filter by Channels

```bash
curl -X GET "https://api.example.com/api/v1/notifications/history?channels=email,push" \
  -H "Authorization: Bearer <token>" \
  -H "x-client-type: web"
```

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "Invalid query parameters",
  "error": "Bad Request"
}
```

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

### 500 Internal Server Error
```json
{
  "statusCode": 500,
  "message": "Internal server error"
}
```

## Related Endpoints

- `GET /api/v1/notifications/history/:id` - Get individual notification
- `PUT /api/v1/notifications/history/:id/read` - Mark notification as read
- `DELETE /api/v1/notifications/history/:id` - Delete notification
- `GET /api/v1/notifications/preferences` - Get notification preferences

## Performance Considerations

- Results are cached for improved performance
- Use pagination for large datasets
- Consider using date filters to limit result sets
- The summary statistics are computed on-the-fly for current page data

## Migration from Mobile API

If migrating from mobile-specific notification endpoints:

1. Add `x-client-type: web` header
2. Update response parsing to handle new fields (`is_read`, `module`, `contextual_ids`, `summary`)
3. Utilize higher pagination limits if needed
4. Leverage summary statistics for dashboard displays
