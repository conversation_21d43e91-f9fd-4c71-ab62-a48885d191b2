import {
  notification_templates,
  notification_types,
} from '../src/db/schema/notification_system';
import { eq } from 'drizzle-orm';
import * as dotenv from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Load environment variables
dotenv.config();

async function seed() {
  console.warn('Seeding notification templates and types...');

  // Create a database connection
  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  const client = postgres(connectionString, {
    ssl: process.env.USE_SSL === 'true',
  });
  const db = drizzle(client);

  // Seed notification templates
  const templates = [
    {
      name: 'Quiz Notification',
      description: 'Template for quiz-related notifications',
      title_template: '{{title}}',
      body_template: '{{body}}',
      email_subject_template: '{{title}}',
      email_body_template: '{{body}} Log in to the app to attempt the quiz.',
    },
    {
      name: 'Post Notification',
      description: 'Template for post-related notifications',
      title_template: '{{title}}',
      body_template: '{{body}}',
      email_subject_template: '{{title}}',
      email_body_template: '{{body}}Log in to the app to view the post.',
    },
    {
      name: 'Announcement Notification',
      description: 'Template for announcements',
      title_template: '{{title}}',
      body_template: '{{body}}',
      email_subject_template: '{{title}}',
      email_body_template: '{{body}}',
    },
  ];

  // Insert templates if they don't exist
  for (const template of templates) {
    const existingTemplate = await db
      .select()
      .from(notification_templates)
      .where(eq(notification_templates.name, template.name));

    if (existingTemplate.length === 0) {
      await db.insert(notification_templates).values(template);
      console.warn(`Created template: ${template.name}`);
    } else {
      console.warn(`Template already exists: ${template.name}`);
    }
  }

  // Get template IDs
  const quizTemplate = await db
    .select()
    .from(notification_templates)
    .where(eq(notification_templates.name, 'Quiz Notification'));

  const postTemplate = await db
    .select()
    .from(notification_templates)
    .where(eq(notification_templates.name, 'Post Notification'));

  const announcementTemplate = await db
    .select()
    .from(notification_templates)
    .where(eq(notification_templates.name, 'Announcement Notification'));

  // Seed notification types
  const types = [
    {
      code: 'new_quiz',
      name: 'New Quiz',
      description: 'Notification for when a new quiz is created',
      module: 'quiz',
      template_id: quizTemplate[0]?.id,
      default_channels: ['email', 'push'],
    },
    {
      code: 'active_quiz',
      name: 'Active Quiz',
      description: 'Notification for when a quiz becomes active',
      module: 'quiz',
      template_id: quizTemplate[0]?.id,
      default_channels: ['push', 'in_app'],
    },
    {
      code: 'new_post',
      name: 'New Post',
      description: 'Notification for when a new post is created',
      module: 'post',
      template_id: postTemplate[0]?.id,
      default_channels: ['push', 'in_app'],
    },
    {
      code: 'new_event',
      name: 'New Event',
      description: 'Notification for when a new event is created',
      module: 'event',
      template_id: postTemplate[0]?.id,
      default_channels: ['email', 'push'],
    },
    {
      code: 'new_opportunity',
      name: 'New Opportunity',
      description: 'Notification for when a new opportunity is created',
      module: 'opportunity',
      template_id: postTemplate[0]?.id,
      default_channels: ['email', 'push'],
    },
    {
      code: 'announcement',
      name: 'Announcement',
      description: 'General announcement notification',
      module: 'announcement',
      template_id: announcementTemplate[0]?.id,
      default_channels: ['email', 'push'],
    },
  ];

  // Insert types if they don't exist
  for (const type of types) {
    if (!type.template_id) {
      console.warn(`Template not found for type: ${type.name}`);
      continue;
    }

    const existingType = await db
      .select()
      .from(notification_types)
      .where(eq(notification_types.code, type.code));

    if (existingType.length === 0) {
      await db.insert(notification_types).values(type);
      console.warn(`Created notification type: ${type.name}`);
    } else {
      console.warn(`Notification type already exists: ${type.name}`);
    }
  }

  console.warn('Notification templates and types seeding completed');

  // Close the database connection
  await client.end();
}

// Run the seed function
seed()
  .then(() => {
    console.warn('Seeding completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error seeding database:', error);
    process.exit(1);
  });
