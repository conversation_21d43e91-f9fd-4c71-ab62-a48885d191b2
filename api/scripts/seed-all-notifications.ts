import { NestFactory } from '@nestjs/core';
import { SeedModule } from '../src/seed/seed.module';
import { NotificationSeedService } from '../src/seed/notification-seed.service';
import { EventNotificationSeedService } from '../src/seed/event-notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const notificationSeedService = app.get(NotificationSeedService);
  const eventNotificationSeedService = app.get(EventNotificationSeedService);

  try {
    console.warn('Starting all notifications seed...');

    // First seed standard notifications
    console.warn('Seeding standard notifications...');
    await notificationSeedService.seed();
    console.warn('Standard notifications seeded successfully');

    // Then seed event notifications
    console.warn('Seeding event notifications...');
    await eventNotificationSeedService.seed();
    console.warn('Event notifications seeded successfully');

    console.warn('All notifications seed completed successfully');
  } catch (error) {
    console.error('Failed to seed all notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
