import { NestFactory } from '@nestjs/core';
import { SeedModule } from '../src/seed/seed.module';
import { NotificationSeedService } from '../src/seed/notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const seedService = app.get(NotificationSeedService);

  try {
    console.warn('Starting notification seed...');
    await seedService.seed();
    console.warn('Notification seed completed successfully');
  } catch (error) {
    console.error('Failed to seed notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
