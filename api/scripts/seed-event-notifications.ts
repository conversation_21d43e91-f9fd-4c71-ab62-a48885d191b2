import { NestFactory } from '@nestjs/core';
import { SeedModule } from '../src/seed/seed.module';
import { EventNotificationSeedService } from '../src/seed/event-notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const seedService = app.get(EventNotificationSeedService);

  try {
    console.warn('Starting event notification seed...');
    await seedService.seed();
    console.warn('Event notification seed completed successfully');
  } catch (error) {
    console.error('Failed to seed event notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
